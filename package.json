{"name": "thunee", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "video-server": "cd server && node videoServer.js"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@types/simple-peer": "^9.11.8", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.4.1", "lucide-react": "^0.474.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-motion": "^0.5.2", "react-router-dom": "^7.2.0", "simple-peer": "^9.11.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.3", "stream-browserify": "^3.0.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.19.0", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@types/node": "^22.13.1", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/uuid": "^10.0.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "babel-plugin-react-compiler": "19.0.0-beta-714736e-20250131", "date-fns": "^4.1.0", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "~3.4.17 ", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "uuid": "^11.1.0", "vite": "^6.1.0"}}