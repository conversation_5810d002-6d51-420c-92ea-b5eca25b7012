/**
 * Game End Utilities
 * Handles game end detection and history tracking
 */

/**
 * Check if the game should end based on ball scores
 * Game ends when a team reaches 12 balls, unless a Khanak extends it to 13
 * @param {Object} lobby - The game lobby
 * @returns {Object} - Game end status and details
 */
function checkGameEnd(lobby) {
  const { ballScores } = lobby;

  if (!ballScores) {
    return { gameEnded: false };
  }

  const team1Balls = ballScores.team1 || 0;
  const team2Balls = ballScores.team2 || 0;

  // Check if any team has reached 12 balls
  const team1At12 = team1Balls >= 12;
  const team2At12 = team2Balls >= 12;

  // Check if there was a winning Khanak call that extends the game to 13 balls
  const hasWinningKhanak = lobby.winningKhanakCall || false;
  const ballLimit = hasWinningKhanak ? 13 : 12;

  console.log(`Checking game end: Team 1: ${team1Balls} balls, Team 2: ${team2Balls} balls, Ball limit: ${ballLimit}`);

  // Game ends when a team reaches the ball limit
  // Special case: if a team reaches 12/13 balls through a 4-ball penalty, they win immediately
  if (team1Balls >= ballLimit || team2Balls >= ballLimit) {
    const winner = team1Balls >= ballLimit ? 1 : 2;
    const winnerBalls = winner === 1 ? team1Balls : team2Balls;
    const loserBalls = winner === 1 ? team2Balls : team1Balls;

    // Check if the winning team reached the limit through a 4-ball penalty
    const wonBy4Ball = winnerBalls >= ballLimit && (winnerBalls === 4 || winnerBalls % 4 === 0);

    console.log(`Game ended! Team ${winner} wins with ${winnerBalls} balls vs ${loserBalls} balls${wonBy4Ball ? ' (won by 4-ball penalty)' : ''}`);

    return {
      gameEnded: true,
      winner,
      finalScores: {
        team1: team1Balls,
        team2: team2Balls
      },
      ballLimit,
      hasWinningKhanak,
      wonBy4Ball
    };
  }

  return { gameEnded: false };
}

/**
 * Initialize game history tracking for a lobby
 * @param {Object} lobby - The game lobby
 */
function initializeGameHistory(lobby) {
  if (!lobby.gameHistory) {
    lobby.gameHistory = {
      balls: [], // Array of ball results
      startTime: new Date(),
      totalBalls: 0
    };
  }
}

/**
 * Add a ball result to the game history
 * @param {Object} lobby - The game lobby
 * @param {Object} ballResult - The ball result data
 */
function addBallToHistory(lobby, ballResult) {
  initializeGameHistory(lobby);

  const ballHistoryEntry = {
    ballNumber: ballResult.ballId || (lobby.gameHistory.totalBalls + 1),
    winner: ballResult.winner,
    ballsAwarded: ballResult.ballsAwarded || 1,
    points: ballResult.points || { team1: 0, team2: 0 },
    ballScores: { ...ballResult.ballScores },
    timestamp: new Date(),
    specialCall: null,
    details: {}
  };

  // Add special call information
  if (ballResult.doubleProcessed) {
    ballHistoryEntry.specialCall = 'Double';
    ballHistoryEntry.details.doubleResult = ballResult.doubleResult;
  } else if (ballResult.khanakInvalid || ballResult.khanakValid) {
    ballHistoryEntry.specialCall = 'Khanak';
    ballHistoryEntry.details.khanakResult = ballResult.khanakResult;
  } else if (ballResult.fourBallAwarded) {
    ballHistoryEntry.specialCall = 'Four Ball';
    ballHistoryEntry.details.fourBallOption = ballResult.fourBallOption;
  } else if (ballResult.timeout) {
    ballHistoryEntry.specialCall = 'Timeout';
    ballHistoryEntry.details.timedOutPlayer = ballResult.timedOutPlayer;
  } else if (ballResult.isCallAndLost) {
    ballHistoryEntry.specialCall = 'Call and Lost';
  }

  lobby.gameHistory.balls.push(ballHistoryEntry);
  lobby.gameHistory.totalBalls++;

  console.log(`Added ball ${ballHistoryEntry.ballNumber} to game history. Winner: Team ${ballResult.winner}, Balls awarded: ${ballHistoryEntry.ballsAwarded}`);
}

/**
 * Get the complete game history for display
 * @param {Object} lobby - The game lobby
 * @returns {Object} - Complete game history
 */
function getGameHistory(lobby) {
  initializeGameHistory(lobby);

  const history = {
    ...lobby.gameHistory,
    endTime: new Date(),
    duration: new Date() - lobby.gameHistory.startTime
  };

  return history;
}

/**
 * Mark a Khanak call as winning (extends game to 13 balls)
 * @param {Object} lobby - The game lobby
 * @param {Object} khanakResult - The Khanak result
 */
function markWinningKhanak(lobby, khanakResult) {
  if (khanakResult.success && khanakResult.ballsAwarded === 3) {
    lobby.winningKhanakCall = true;
    console.log('Marked winning Khanak call - game extended to 13 balls');
  }
}

/**
 * Check if special ball limit rules apply for Double/Khanak calls
 * These rules prevent teams from winning too easily when close to victory
 * Note: Thunee calls are NOT subject to these rules and can be called in any ball
 * @param {Object} lobby - The game lobby
 * @param {number} callerTeam - The team making the call (1 or 2)
 * @param {string} callType - Type of call ('double', 'khanak', or 'thunee')
 * @returns {Object} - Whether special rules apply and what the outcome should be
 */
function checkSpecialBallLimitRules(lobby, callerTeam, callType) {
  const { ballScores } = lobby;

  if (!ballScores) {
    return { applySpecialRules: false };
  }

  const callerBalls = ballScores[`team${callerTeam}`] || 0;
  const opposingTeam = callerTeam === 1 ? 2 : 1;

  // Determine if this is a 12-ball or 13-ball game
  const hasWinningKhanak = lobby.winningKhanakCall || false;
  const ballLimit = hasWinningKhanak ? 13 : 12;

  console.log(`Checking special ball limit rules: Team ${callerTeam} has ${callerBalls} balls, Ball limit: ${ballLimit}, Call type: ${callType}`);

  // Thunee calls are exempt from special ball limit rules
  if (callType === 'thunee') {
    console.log('Thunee calls are exempt from special ball limit rules');
    return { applySpecialRules: false };
  }

  let shouldApplySpecialRules = false;

  if (callType === 'double') {
    if (ballLimit === 12) {
      // 12 ball game: Double at 11 or 12 balls triggers special rules
      shouldApplySpecialRules = callerBalls >= 11;
    } else {
      // 13 ball game: Double at 12 balls triggers special rules
      shouldApplySpecialRules = callerBalls >= 12;
    }
  } else if (callType === 'khanak') {
    if (ballLimit === 12) {
      // 12 ball game: Khanak at 10, 11, or 12 balls triggers special rules
      shouldApplySpecialRules = callerBalls >= 10;
    } else {
      // 13 ball game: Khanak at 11 or 12 balls triggers special rules
      shouldApplySpecialRules = callerBalls >= 11;
    }
  }

  if (shouldApplySpecialRules) {
    console.log(`Special ball limit rules apply! Team ${callerTeam} called ${callType} with ${callerBalls} balls in a ${ballLimit}-ball game. Opposing team gets 4 balls, caller gets none.`);

    return {
      applySpecialRules: true,
      ballsAwarded: 4,
      winningTeam: opposingTeam,
      outcome: 'special_ball_limit_rule',
      reason: `${callType} called with ${callerBalls} balls in ${ballLimit}-ball game`
    };
  }

  return { applySpecialRules: false };
}

module.exports = {
  checkGameEnd,
  initializeGameHistory,
  addBallToHistory,
  getGameHistory,
  markWinningKhanak,
  checkSpecialBallLimitRules
};
